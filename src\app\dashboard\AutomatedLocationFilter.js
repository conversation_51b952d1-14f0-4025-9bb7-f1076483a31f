import React, { useState, useEffect } from "react";
import ReactSelect from 'react-select';
import { MultiSelect } from 'primereact/multiselect';
import { Button } from 'primereact/button';
import API from '../services/API';
import { PROJECTS_URL } from '../constants';

const AutomatedLocationFilter = ({ onSelectionChange }) => {
    const countries = [
        { name: 'UK', id: 'UK' },
        { name: 'India', id: 'IN' },
        { name: 'Singapore', id: 'SG' },
        { name: 'Thailand', id: 'TH' },
        { name: 'Indonesia', id: 'ID' },
        { name: 'Philippines', id: 'PH' },
        { name: 'Japan', id: 'JP' },
        { name: 'Korea', id: 'KR' }
    ];

    const buLevels = [
        { name: 'DC Ops', id: 'DC' },
        { name: 'Construction', id: 'Construction' },
        { name: 'Office', id: 'Office' }
    ];

    const [mergedSites, setMergedSites] = useState([]); // New state to hold merged sites


   
    // Assume countries and other data structures are defined outside this component
    const [selectedCountry, setSelectedCountry] = useState([...countries]);
    const [selectedBuLevel, setSelectedBuLevel] = useState([...buLevels]);
    const [selectedSite, setSelectedSite] = useState([]);
    const [isInitialLoad, setIsInitialLoad] = useState(true);

    useEffect(() => {
        // Fetch projects from API
        const fetchProjects = async () => {
            try {
                const response = await API.get(PROJECTS_URL);

                if (response.status === 200 && response.data && response.data.length > 0) {
                    // Remove duplicates by using a Set
                    const mergedList = [...response.data].reduce((acc, site) => {
                        if (!acc.some(s => s.id === site.id)) acc.push(site);
                        return acc;
                    }, []);

                    setMergedSites(mergedList);
                }
            } catch (error) {
                console.error('Error fetching projects:', error);
            }
        };

        fetchProjects();
    }, []);

    useEffect(() => {
        if (selectedCountry.length === 0) {
            setSelectedBuLevel([]);
            setSelectedSite([]);
        }
    }, [selectedCountry]);

    useEffect(() => {
        // Filter sites based on selected countries and BU Levels
        const countryIds = selectedCountry.map(country => country.id);
        const buIds = selectedBuLevel.map(bu => bu.id);

        if (buIds.length === 0) {
            setSelectedSite([]);
        } else {
            const filteredSites = mergedSites.filter(site =>
                countryIds.includes(site.country) &&
                buIds.includes(site.bu)
            );
            setSelectedSite(filteredSites);
        }
    }, [selectedCountry, selectedBuLevel, mergedSites]);

    // Apply filter automatically on initial load only
    useEffect(() => {
        if (isInitialLoad && mergedSites.length > 0 && selectedSite.length > 0) {
            // Calculate expected sites based on current country and BU selections
            const countryIds = selectedCountry.map(country => country.id);
            const buIds = selectedBuLevel.map(bu => bu.id);
            const expectedSites = mergedSites.filter(site =>
                countryIds.includes(site.country) &&
                buIds.includes(site.bu)
            );

            // Only trigger if all expected sites are selected
            if (selectedSite.length === expectedSites.length) {
                onSelectionChange({
                    countries: selectedCountry,
                    buLevels: selectedBuLevel,
                    sites: selectedSite
                });
                setIsInitialLoad(false);
            }
        }
    }, [selectedCountry, selectedBuLevel, selectedSite, mergedSites, isInitialLoad, onSelectionChange]);

    // Apply Filter function
    const handleApplyFilter = () => {
        onSelectionChange({
            countries: selectedCountry,
            buLevels: selectedBuLevel,
            sites: selectedSite
        });
    };

    const handleCountryChange = (e) => {
        setSelectedCountry(e.value);
    };

    const handleBuLevelChange = (e) => {
        setSelectedBuLevel(e.value);
    };

    const handleSiteChange = (e) => {
        const alwaysSelectedCountries = selectedSite.filter(option => option.uncheckDisable);
        const newSelection = [...e.value, ...alwaysSelectedCountries];

        // Removing duplicates if any (optional, based on your logic)
        const uniqueNewSelection = Array.from(new Set(newSelection.map(a => a.id)))
            .map(id => {
                return newSelection.find(a => a.id === id)
            });
        setSelectedSite(uniqueNewSelection);
    };

    const generateSelectedItemsLabel = (selectedItems, totalItems, itemName) => {
        if (selectedItems.length === totalItems.length) {
            return `All ${itemName}`;
        } else {
            return `${selectedItems.length} ${itemName} selected`;
        }
    };


    return (
        <div className="container p-0">
            <div className="row">
                <div className="col-3">
                    <label>Country</label>
                    <MultiSelect
                        className="w-100"
                        value={selectedCountry}
                        options={countries}
                        onChange={handleCountryChange}
                        optionLabel="name"
                        placeholder="Select Countries"
                        maxSelectedLabels={3}
                        selectedItemsLabel={generateSelectedItemsLabel(selectedCountry, countries, "Countries")}
                    />
                </div>
                <div className="col-3">
                    <label>Business Unit</label>
                    <MultiSelect
                        className="w-100"
                        value={selectedBuLevel}
                        options={buLevels}
                        onChange={handleBuLevelChange}
                        optionLabel="name"
                        placeholder="Select BU"
                        maxSelectedLabels={2}
                        selectedItemsLabel={generateSelectedItemsLabel(selectedBuLevel, buLevels, "BU")}
                        disabled={!selectedCountry.length}
                    />
                </div>
                <div className="col-3">
                    <label>Site</label>
                    <MultiSelect
                        className="w-100"
                        value={selectedSite}
                        options={selectedBuLevel.length
                            ? mergedSites.filter(
                                (site) =>
                                    selectedCountry.map((c) => c.id).includes(site.country) &&
                                    selectedBuLevel.map((bu) => bu.id).includes(site.bu)
                            )
                            : []
                        }
                        onChange={handleSiteChange}
                        optionLabel="name"
                        placeholder="Select Site"
                        maxSelectedLabels={3}
                        selectedItemsLabel={generateSelectedItemsLabel(selectedSite, mergedSites, "Sites")}
                        disabled={!selectedBuLevel.length}
                    />
                </div>
                <div className="col-3 d-flex align-items-end">
                    <Button
                        label="Apply Filter"
                        icon="pi pi-filter"
                        onClick={handleApplyFilter}
                        className="w-100"
                        severity="primary"
                    />
                </div>
            </div>
        </div>
    );
};

export default AutomatedLocationFilter;
